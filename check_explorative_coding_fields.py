#!/usr/bin/env python3
"""
检查 explorative_coding 数据的字段结构
"""

from pymongo import MongoClient

def main():
    """主函数"""
    print("🔍 检查 explorative_coding 数据字段...")
    
    # 连接数据库
    client = MongoClient("localhost", 27017)
    db = client["LLM-UQ"]
    collection = db["response_collections"]
    
    # 获取一个样本
    sample = collection.find_one({
        "task_name": "explorative_coding",
        "dataset_source": "pytorch_commits"
    })
    
    if sample:
        print("📋 样本数据字段:")
        for key, value in sample.items():
            if isinstance(value, str) and len(value) > 100:
                print(f"  {key}: {value[:100]}... (长度: {len(value)})")
            else:
                print(f"  {key}: {value}")
        
        # 检查可能的响应字段
        response_fields = ['response', 'parsed_answer', 'answer', 'output', 'result']
        print(f"\n🔍 检查可能的响应字段:")
        for field in response_fields:
            if field in sample:
                value = sample[field]
                if value:
                    print(f"  ✅ {field}: 存在，类型: {type(value)}, 长度: {len(str(value))}")
                    if isinstance(value, str) and len(value) > 0:
                        print(f"      内容预览: {value[:200]}...")
                else:
                    print(f"  ❌ {field}: 存在但为空")
            else:
                print(f"  ❌ {field}: 不存在")
    else:
        print("❌ 没有找到样本数据")
    
    client.close()

if __name__ == "__main__":
    main()
