#!/usr/bin/env python3
"""
检查最新的 explorative_coding 数据，确认响应字段
"""

from pymongo import MongoClient
from datetime import datetime

def main():
    """主函数"""
    print("🔍 检查最新的 explorative_coding 数据...")
    
    # 连接数据库
    client = MongoClient("localhost", 27017)
    db = client["LLM-UQ"]
    collection = db["response_collections"]
    
    # 获取最新的几条记录
    latest_records = list(collection.find({
        "task_name": "explorative_coding",
        "dataset_source": "pytorch_commits"
    }).sort("_id", -1).limit(10))
    
    print(f"📊 找到 {len(latest_records)} 条最新记录")
    
    if latest_records:
        print("\n📋 最新记录的字段分析:")
        
        for i, record in enumerate(latest_records[:3], 1):
            print(f"\n--- 记录 {i} ---")
            print(f"ID: {record.get('_id')}")
            print(f"Task ID: {record.get('task_id', 'N/A')}")
            print(f"Prompt Seed: {record.get('prompt_seed', 'N/A')}")
            print(f"Execution Time: {record.get('execution_timestamp', 'N/A')}")
            
            # 检查各种响应字段
            response_fields = {
                'raw_answer': record.get('raw_answer'),
                'parsed_answer': record.get('parsed_answer'),
                'actual_response': record.get('actual_response'),
                'raw_response': record.get('raw_response')
            }
            
            print(f"响应字段状态:")
            for field_name, field_value in response_fields.items():
                if field_value is not None and field_value != "":
                    print(f"  ✅ {field_name}: 有内容 (长度: {len(str(field_value))})")
                    print(f"      预览: {str(field_value)[:150]}...")
                elif field_value is None:
                    print(f"  ❌ {field_name}: None")
                else:
                    print(f"  ❌ {field_name}: 空字符串")
    
    # 检查有30个响应的组（新增的5组）
    print(f"\n" + "="*60)
    print("🎯 检查有30个响应的数据组（新增的5组）")
    print("="*60)
    
    pipeline = [
        {
            "$match": {
                "task_name": "explorative_coding",
                "dataset_source": "pytorch_commits"
            }
        },
        {
            "$group": {
                "_id": {
                    "prompt_seed": "$prompt_seed",
                    "input_text": "$input_text"
                },
                "count": {"$sum": 1},
                "sample_doc": {"$first": "$$ROOT"}
            }
        },
        {
            "$match": {
                "count": 30  # 只看有30个响应的组
            }
        }
    ]
    
    groups_30 = list(collection.aggregate(pipeline))
    print(f"📊 找到 {len(groups_30)} 个有30个响应的数据组")
    
    if groups_30:
        for i, group in enumerate(groups_30, 1):
            print(f"\n--- 组 {i} ---")
            print(f"Prompt Seed: {group['_id']['prompt_seed']}")
            print(f"Input Text: {group['_id']['input_text'][:100]}...")
            print(f"Response Count: {group['count']}")
            
            # 检查这个组的样本响应字段
            sample = group['sample_doc']
            response_fields = {
                'raw_answer': sample.get('raw_answer'),
                'parsed_answer': sample.get('parsed_answer'),
                'actual_response': sample.get('actual_response'),
                'raw_response': sample.get('raw_response')
            }
            
            print(f"样本响应字段:")
            for field_name, field_value in response_fields.items():
                if field_value is not None and field_value != "":
                    print(f"  ✅ {field_name}: 有内容 (长度: {len(str(field_value))})")
                elif field_value is None:
                    print(f"  ❌ {field_name}: None")
                else:
                    print(f"  ❌ {field_name}: 空字符串")
    
    # 总结建议
    print(f"\n" + "="*60)
    print("💡 建议使用的响应字段")
    print("="*60)
    
    # 统计各字段的使用情况
    field_stats = {}
    sample_records = collection.find({
        "task_name": "explorative_coding",
        "dataset_source": "pytorch_commits"
    }).limit(100)
    
    for record in sample_records:
        for field in ['raw_answer', 'parsed_answer', 'actual_response', 'raw_response']:
            if field not in field_stats:
                field_stats[field] = {'has_content': 0, 'total': 0}
            
            field_stats[field]['total'] += 1
            value = record.get(field)
            if value is not None and value != "":
                field_stats[field]['has_content'] += 1
    
    print("字段使用统计（基于100个样本）:")
    for field, stats in field_stats.items():
        percentage = (stats['has_content'] / stats['total']) * 100 if stats['total'] > 0 else 0
        print(f"  {field}: {stats['has_content']}/{stats['total']} ({percentage:.1f}%)")
    
    # 找出最佳字段
    best_field = max(field_stats.items(), key=lambda x: x[1]['has_content'])
    print(f"\n🎯 建议使用字段: {best_field[0]} (使用率: {(best_field[1]['has_content']/best_field[1]['total']*100):.1f}%)")
    
    client.close()

if __name__ == "__main__":
    main()
