#!/usr/bin/env python3
"""
检查 sentiment_analysis 的 UQ 分析状态
找出还没有完成 UQ 分析的项目
"""

import os
import sys
from pymongo import MongoClient
from typing import Dict, List, Set, Any
import yaml
from collections import defaultdict

def connect_mongodb():
    """连接到 MongoDB"""
    try:
        client = MongoClient("localhost", 27017)
        db = client["LLM-UQ"]
        return client, db
    except Exception as e:
        print(f"❌ MongoDB 连接失败: {e}")
        sys.exit(1)

def get_all_sentiment_groups(db):
    """获取所有 sentiment_analysis 的数据组"""
    collection = db["response_collections"]
    
    # 查询所有 sentiment_analysis 的数据
    pipeline = [
        {
            "$match": {
                "task_name": "sentiment_analysis",
                "dataset_source": "twitter_sentiment"
            }
        },
        {
            "$group": {
                "_id": {
                    "task_name": "$task_name",
                    "dataset_source": "$dataset_source", 
                    "prompt_seed": "$prompt_seed",
                    "input_text": "$input_text",
                    "llm_model": "$llm_model"
                },
                "count": {"$sum": 1},
                "responses": {"$push": "$parsed_answer"}
            }
        },
        {
            "$match": {
                "count": {"$gte": 2}  # 至少需要2个响应才能进行UQ分析
            }
        }
    ]
    
    groups = list(collection.aggregate(pipeline))
    print(f"📊 找到 {len(groups)} 个 sentiment_analysis 数据组")
    return groups

def get_completed_uq_analysis(db):
    """获取已完成的 UQ 分析"""
    collection = db["UQ_result_sentiment_analysis"]
    
    # 查询所有已完成的分析
    completed = list(collection.find({}, {
        "group_key": 1,
        "uq_results": 1,
        "metadata": 1
    }))
    
    print(f"📈 找到 {len(completed)} 个已完成的 UQ 分析结果")
    return completed

def get_available_uq_methods():
    """获取所有可用的 UQ 方法"""
    # 从配置文件中读取所有可用的方法
    config_path = "configs/uq_analysis_config.yaml"
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        return config["uq_methods"]["enabled_methods"]
    else:
        # 默认的所有方法
        return [
            "EigValLaplacianJaccardUQ",
            "EigValLaplacianNLIUQ", 
            "EccentricityJaccardUQ",
            "EccentricityNLIEntailUQ",
            "SemanticEntropyNLIUQ",
            "EmbeddingQwenUQ",
            "EmbeddingE5UQ",
            "LUQUQ",
            "LofreeCPUQ",
            "NumSetsUQ",
            "KernelLanguageEntropyUQ"
        ]

def build_group_key(group_id):
    """构建组键，用于匹配"""
    return {
        "task_name": group_id["task_name"],
        "dataset_source": group_id["dataset_source"],
        "prompt_seed": group_id["prompt_seed"],
        "input_text": group_id["input_text"],
        "llm_model": group_id.get("llm_model", "qwen3-32b")
    }

def analyze_missing_uq(groups, completed_analysis, all_methods):
    """分析缺失的 UQ 分析"""

    # 构建已完成分析的索引
    completed_index = defaultdict(set)
    groups_with_any_analysis = set()

    for analysis in completed_analysis:
        group_key = analysis["group_key"]
        key = (
            group_key["task_name"],
            group_key["dataset_source"],
            group_key["prompt_seed"],
            group_key["input_text"],
            group_key.get("llm_model", "qwen3-32b")
        )

        groups_with_any_analysis.add(key)

        # 记录已完成的方法
        if "uq_results" in analysis:
            for method_name, result in analysis["uq_results"].items():
                if result.get("status") == "success":
                    completed_index[key].add(method_name)

    # 分析每个组的缺失情况
    missing_analysis = []
    groups_with_complete_analysis = 0
    groups_with_partial_analysis = 0
    groups_with_no_analysis = 0

    for group in groups:
        group_id = group["_id"]
        key = (
            group_id["task_name"],
            group_id["dataset_source"],
            group_id["prompt_seed"],
            group_id["input_text"],
            group_id.get("llm_model", "qwen3-32b")
        )

        completed_methods = completed_index.get(key, set())
        missing_methods = set(all_methods) - completed_methods

        if not completed_methods:
            groups_with_no_analysis += 1
        elif missing_methods:
            groups_with_partial_analysis += 1
        else:
            groups_with_complete_analysis += 1

        if missing_methods:
            missing_analysis.append({
                "group": group,
                "missing_methods": list(missing_methods),
                "completed_methods": list(completed_methods),
                "response_count": group["count"],
                "analysis_status": "no_analysis" if not completed_methods else "partial_analysis"
            })

    print(f"\n📊 数据组分析状态详情:")
    print(f"  完全完成分析的组: {groups_with_complete_analysis}")
    print(f"  部分完成分析的组: {groups_with_partial_analysis}")
    print(f"  完全没有分析的组: {groups_with_no_analysis}")
    print(f"  总数据组: {len(groups)}")
    print(f"  验证: {groups_with_complete_analysis + groups_with_partial_analysis + groups_with_no_analysis} = {len(groups)}")

    return missing_analysis

def print_analysis_summary(missing_analysis, all_methods):
    """打印分析摘要"""
    print("\n" + "="*80)
    print("📋 SENTIMENT ANALYSIS UQ 分析状态摘要")
    print("="*80)
    
    total_groups = len(missing_analysis)
    total_methods = len(all_methods)
    
    if total_groups == 0:
        print("🎉 所有 sentiment_analysis 数据都已完成 UQ 分析！")
        return
    
    print(f"📊 需要补充分析的数据组: {total_groups}")
    print(f"🔧 总 UQ 方法数: {total_methods}")
    
    # 统计每个方法的缺失情况
    method_missing_count = defaultdict(int)
    for item in missing_analysis:
        for method in item["missing_methods"]:
            method_missing_count[method] += 1
    
    print(f"\n📈 各方法缺失统计:")
    for method in sorted(all_methods):
        missing_count = method_missing_count[method]
        completion_rate = (total_groups - missing_count) / total_groups * 100
        print(f"  {method:25s}: 缺失 {missing_count:4d} 组 (完成率: {completion_rate:5.1f}%)")
    
    # 统计响应数量分布
    response_counts = [item["response_count"] for item in missing_analysis]
    print(f"\n📊 响应数量分布:")
    print(f"  最少响应数: {min(response_counts)}")
    print(f"  最多响应数: {max(response_counts)}")
    print(f"  平均响应数: {sum(response_counts)/len(response_counts):.1f}")
    
    # 显示前几个需要分析的组
    print(f"\n🔍 前 5 个需要分析的数据组:")
    for i, item in enumerate(missing_analysis[:5]):
        group_id = item["group"]["_id"]
        print(f"  {i+1}. Seed: {group_id['prompt_seed']}, "
              f"Responses: {item['response_count']}, "
              f"Missing: {len(item['missing_methods'])} methods")
        print(f"     Input: {group_id['input_text'][:50]}...")

def main():
    """主函数"""
    print("🔍 检查 sentiment_analysis UQ 分析状态...")
    
    # 连接数据库
    client, db = connect_mongodb()
    
    try:
        # 获取所有数据组
        groups = get_all_sentiment_groups(db)
        
        # 获取已完成的分析
        completed_analysis = get_completed_uq_analysis(db)
        
        # 获取所有可用方法
        all_methods = get_available_uq_methods()
        print(f"🔧 可用 UQ 方法: {len(all_methods)} 个")
        
        # 分析缺失情况
        missing_analysis = analyze_missing_uq(groups, completed_analysis, all_methods)
        
        # 打印摘要
        print_analysis_summary(missing_analysis, all_methods)
        
        # 保存缺失分析结果到文件
        if missing_analysis:
            output_file = "sentiment_missing_uq_analysis.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"需要补充 UQ 分析的 sentiment_analysis 数据组: {len(missing_analysis)}\n\n")
                for i, item in enumerate(missing_analysis):
                    group_id = item["group"]["_id"]
                    f.write(f"{i+1}. Prompt Seed: {group_id['prompt_seed']}\n")
                    f.write(f"   Input Text: {group_id['input_text']}\n")
                    f.write(f"   Response Count: {item['response_count']}\n")
                    f.write(f"   Missing Methods: {', '.join(item['missing_methods'])}\n")
                    f.write(f"   Completed Methods: {', '.join(item['completed_methods'])}\n\n")
            
            print(f"\n💾 详细信息已保存到: {output_file}")
        
    finally:
        client.close()

if __name__ == "__main__":
    main()
