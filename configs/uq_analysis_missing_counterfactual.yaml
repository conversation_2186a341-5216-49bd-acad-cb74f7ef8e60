# YAML config for missing counterfactual QA UQ analysis
uq_analysis:
  mongo:
    host: localhost
    port: 27017
    db: LLM-UQ
    source_collection: response_collections
    results_collection: UQ_result_counterfactual_qa
    matrix_collection: uq_matrix
    embedding_cache_collection: embeddings_cache

  filters:
    dataset_source: counterfactual_data   # Focus on counterfactual QA
    prompt_variant: null                  # All prompt variants

  methods:
    - eigval_jaccard
    - ecc_jaccard
    - eigval_nli
    - ecc_nli
    - se
    - numsets
    - embed_qwen
    - embed_e5
    - kle
    - luq
    - lofreecp
    # GPT-OSS-20B methods
    - eigval_nli_gpt_oss
    - ecc_nli_gpt_oss
    - se_gpt_oss
    - numsets_gpt_oss
    - luq_gpt_oss
    - luq_sentence_gpt_oss
    - kle_gpt_oss

  limit_groups: 0  # 0 means no limit
