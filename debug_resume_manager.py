#!/usr/bin/env python3
"""
调试 ResumeManager 的 filter_pending_work 方法
"""

import sys
import os
from pymongo import MongoClient
import logging

sys.path.append(os.path.dirname(__file__))

from uq_analysis.resume_manager import ResumeManager

# 设置日志
logging.basicConfig(level=logging.DEBUG)
log = logging.getLogger(__name__)

def main():
    """调试 ResumeManager"""
    print("🔍 调试 ResumeManager...")
    
    # 连接MongoDB
    client = MongoClient("localhost", 27017)
    db = client["LLM-UQ"]
    collection = db["response_collections"]
    
    # 获取一个有30个响应的数据组
    pipeline = [
        {
            "$match": {
                "task_name": "sentiment_analysis",
                "dataset_source": "twitter_sentiment"
            }
        },
        {
            "$group": {
                "_id": {
                    "task_name": "$task_name",
                    "dataset_source": "$dataset_source",
                    "prompt_seed": "$prompt_seed",
                    "input_text": "$input_text",
                    "llm_model": "$llm_model"
                },
                "count": {"$sum": 1},
                "docs": {"$push": "$$ROOT"}
            }
        },
        {
            "$match": {
                "count": {"$eq": 30}
            }
        },
        {
            "$limit": 3
        }
    ]
    
    groups = list(collection.aggregate(pipeline))
    
    if not groups:
        print("❌ 没有找到数据组")
        return
    
    print(f"📊 找到 {len(groups)} 个数据组")
    
    # 检查原始数据组结构
    for i, group in enumerate(groups):
        print(f"\n数据组 {i+1}:")
        print(f"  Keys: {list(group.keys())}")
        print(f"  Has 'docs': {'docs' in group}")
        print(f"  Docs count: {len(group.get('docs', []))}")
    
    # 初始化 ResumeManager
    resume_config = {
        "enabled": True,
        "save_interval": 10
    }
    
    resume_manager = ResumeManager(resume_config)
    
    # 获取输出集合
    output_collection = db["UQ_result_sentiment_analysis"]
    
    # 加载已完成的工作
    method_names = ["SemanticEntropyNLIUQ", "NumSetsUQ"]
    resume_manager.load_completed_work(output_collection, "sentiment_analysis", method_names)
    
    # 过滤待处理的工作
    print("\n🔄 过滤待处理的工作...")
    pending_work = resume_manager.filter_pending_work(
        groups, method_names, "UQ_result_sentiment_analysis")
    
    print(f"📋 待处理工作: {len(pending_work)} 个")
    
    # 检查过滤后的数据结构
    for i, (group, pending_methods) in enumerate(pending_work[:2]):
        print(f"\n待处理工作 {i+1}:")
        print(f"  Group type: {type(group)}")
        print(f"  Group keys: {list(group.keys())}")
        print(f"  Has 'docs': {'docs' in group}")
        print(f"  Docs count: {len(group.get('docs', []))}")
        print(f"  Pending methods: {pending_methods}")
        
        # 检查 docs 内容
        if 'docs' in group and group['docs']:
            first_doc = group['docs'][0]
            print(f"  First doc keys: {list(first_doc.keys())[:10]}...")  # 只显示前10个键
            print(f"  First doc has 'parsed_answer': {'parsed_answer' in first_doc}")
    
    client.close()

if __name__ == "__main__":
    main()
