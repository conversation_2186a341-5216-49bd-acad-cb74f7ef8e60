#!/usr/bin/env python3
"""
调试 sentiment_analysis 数据结构
"""

import sys
import os
from pymongo import MongoClient
import json

def main():
    """检查数据结构"""
    print("🔍 检查 sentiment_analysis 数据结构...")
    
    # 连接MongoDB
    client = MongoClient("localhost", 27017)
    db = client["LLM-UQ"]
    collection = db["response_collections"]
    
    # 获取一个有30个响应的数据组
    pipeline = [
        {
            "$match": {
                "task_name": "sentiment_analysis",
                "dataset_source": "twitter_sentiment"
            }
        },
        {
            "$group": {
                "_id": {
                    "task_name": "$task_name",
                    "dataset_source": "$dataset_source",
                    "prompt_seed": "$prompt_seed",
                    "input_text": "$input_text",
                    "llm_model": "$llm_model"
                },
                "count": {"$sum": 1},
                "docs": {"$push": "$$ROOT"}
            }
        },
        {
            "$match": {
                "count": {"$eq": 30}
            }
        },
        {
            "$limit": 1
        }
    ]
    
    groups = list(collection.aggregate(pipeline))
    
    if groups:
        group = groups[0]
        print(f"📊 找到数据组:")
        print(f"  Group ID: {group['_id']}")
        print(f"  Count: {group['count']}")
        print(f"  Has 'docs' field: {'docs' in group}")
        
        if 'docs' in group:
            print(f"  Docs length: {len(group['docs'])}")
            if group['docs']:
                first_doc = group['docs'][0]
                print(f"  First doc keys: {list(first_doc.keys())}")
                print(f"  First doc parsed_answer: {first_doc.get('parsed_answer', 'NOT FOUND')}")
        
        # 保存样本到文件
        with open('sample_sentiment_group.json', 'w', encoding='utf-8') as f:
            json.dump(group, f, indent=2, ensure_ascii=False, default=str)
        print("💾 样本数据已保存到 sample_sentiment_group.json")
    else:
        print("❌ 没有找到有30个响应的数据组")
    
    client.close()

if __name__ == "__main__":
    main()
