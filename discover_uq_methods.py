#!/usr/bin/env python3
"""
发现所有可用的UQ方法
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from uq_analysis.method_loader import UQMethodLoader

def main():
    """发现并列出所有可用的UQ方法"""
    print("🔍 发现UQ方法...")
    
    loader = UQMethodLoader()
    discovered_methods = loader.discover_methods()
    
    print(f"\n📊 发现 {len(discovered_methods)} 个UQ方法:")
    for i, method_name in enumerate(discovered_methods, 1):
        print(f"  {i:2d}. {method_name}")
    
    print(f"\n📋 配置文件格式:")
    print("enabled_methods:")
    for method_name in discovered_methods:
        print(f"  - \"{method_name}\"")

if __name__ == "__main__":
    main()
