#!/usr/bin/env python3
"""
探索数据库中的commit相关数据
"""

import os
import sys
from pymongo import MongoClient
from collections import defaultdict

def connect_mongodb():
    """连接到 MongoDB"""
    try:
        client = MongoClient("localhost", 27017)
        db = client["LLM-UQ"]
        return client, db
    except Exception as e:
        print(f"❌ MongoDB 连接失败: {e}")
        sys.exit(1)

def explore_all_tasks(db):
    """探索所有任务类型"""
    collection = db["response_collections"]
    
    # 获取所有不同的任务类型和数据源
    pipeline = [
        {
            "$group": {
                "_id": {
                    "task_name": "$task_name",
                    "dataset_source": "$dataset_source"
                },
                "count": {"$sum": 1}
            }
        },
        {
            "$sort": {"_id.task_name": 1, "_id.dataset_source": 1}
        }
    ]
    
    results = list(collection.aggregate(pipeline))
    
    print("📊 数据库中的所有任务类型和数据源:")
    for result in results:
        task_name = result["_id"]["task_name"]
        dataset_source = result["_id"]["dataset_source"]
        count = result["count"]
        print(f"  {task_name} / {dataset_source}: {count} 条记录")
    
    return results

def explore_commit_related_data(db):
    """探索commit相关的数据"""
    collection = db["response_collections"]
    
    # 查找包含commit的任务
    commit_tasks = []
    
    # 方法1: 查找task_name包含commit的
    pipeline1 = [
        {
            "$match": {
                "task_name": {"$regex": "commit", "$options": "i"}
            }
        },
        {
            "$group": {
                "_id": {
                    "task_name": "$task_name",
                    "dataset_source": "$dataset_source"
                },
                "count": {"$sum": 1}
            }
        }
    ]
    
    results1 = list(collection.aggregate(pipeline1))
    if results1:
        print("\n🔍 包含'commit'的任务名:")
        for result in results1:
            print(f"  {result['_id']['task_name']} / {result['_id']['dataset_source']}: {result['count']} 条记录")
            commit_tasks.extend([result])
    
    # 方法2: 查找dataset_source包含commit的
    pipeline2 = [
        {
            "$match": {
                "dataset_source": {"$regex": "commit", "$options": "i"}
            }
        },
        {
            "$group": {
                "_id": {
                    "task_name": "$task_name",
                    "dataset_source": "$dataset_source"
                },
                "count": {"$sum": 1}
            }
        }
    ]
    
    results2 = list(collection.aggregate(pipeline2))
    if results2:
        print("\n🔍 包含'commit'的数据源:")
        for result in results2:
            print(f"  {result['_id']['task_name']} / {result['_id']['dataset_source']}: {result['count']} 条记录")
            commit_tasks.extend([result])
    
    # 方法3: 查找可能的coding相关任务
    pipeline3 = [
        {
            "$match": {
                "$or": [
                    {"task_name": {"$regex": "cod", "$options": "i"}},
                    {"dataset_source": {"$regex": "cod", "$options": "i"}}
                ]
            }
        },
        {
            "$group": {
                "_id": {
                    "task_name": "$task_name",
                    "dataset_source": "$dataset_source"
                },
                "count": {"$sum": 1}
            }
        }
    ]
    
    results3 = list(collection.aggregate(pipeline3))
    if results3:
        print("\n🔍 包含'cod'的任务:")
        for result in results3:
            print(f"  {result['_id']['task_name']} / {result['_id']['dataset_source']}: {result['count']} 条记录")
            commit_tasks.extend([result])
    
    return commit_tasks

def get_sample_data(db, task_name, dataset_source, limit=3):
    """获取样本数据"""
    collection = db["response_collections"]
    
    samples = list(collection.find({
        "task_name": task_name,
        "dataset_source": dataset_source
    }).limit(limit))
    
    print(f"\n📋 {task_name}/{dataset_source} 的样本数据:")
    for i, sample in enumerate(samples, 1):
        print(f"  样本 {i}:")
        print(f"    ID: {sample.get('_id')}")
        print(f"    Task ID: {sample.get('task_id', 'N/A')}")
        print(f"    Prompt Seed: {sample.get('prompt_seed', 'N/A')}")
        print(f"    Input Text: {sample.get('input_text', 'N/A')[:100]}...")
        response_field = sample.get('parsed_answer', sample.get('response', 'N/A'))
        if response_field and response_field != 'N/A':
            print(f"    Response Field: {str(response_field)[:100]}...")
        else:
            print(f"    Response Field: N/A")
        print(f"    Model: {sample.get('llm_model', sample.get('model_identifier', 'N/A'))}")
        print()

def check_response_counts(db, task_name, dataset_source):
    """检查响应数量分布"""
    collection = db["response_collections"]
    
    pipeline = [
        {
            "$match": {
                "task_name": task_name,
                "dataset_source": dataset_source
            }
        },
        {
            "$group": {
                "_id": {
                    "prompt_seed": "$prompt_seed",
                    "input_text": "$input_text"
                },
                "count": {"$sum": 1}
            }
        },
        {
            "$group": {
                "_id": "$count",
                "groups": {"$sum": 1}
            }
        },
        {
            "$sort": {"_id": 1}
        }
    ]
    
    results = list(collection.aggregate(pipeline))
    
    print(f"\n📊 {task_name}/{dataset_source} 响应数量分布:")
    total_groups = 0
    for result in results:
        response_count = result["_id"]
        group_count = result["groups"]
        total_groups += group_count
        print(f"  {response_count} 个响应: {group_count} 组")
    
    print(f"  总组数: {total_groups}")
    
    return results

def main():
    """主函数"""
    print("🔍 探索数据库中的commit相关数据...")
    
    # 连接数据库
    client, db = connect_mongodb()
    
    try:
        # 探索所有任务类型
        all_tasks = explore_all_tasks(db)
        
        # 探索commit相关数据
        commit_tasks = explore_commit_related_data(db)
        
        # 如果找到commit相关任务，获取详细信息
        if commit_tasks:
            print(f"\n🎯 找到 {len(commit_tasks)} 个可能的commit相关任务")
            
            # 去重
            unique_tasks = {}
            for task in commit_tasks:
                key = (task["_id"]["task_name"], task["_id"]["dataset_source"])
                if key not in unique_tasks:
                    unique_tasks[key] = task
            
            for (task_name, dataset_source), task_info in unique_tasks.items():
                print(f"\n" + "="*60)
                print(f"📋 分析任务: {task_name} / {dataset_source}")
                print("="*60)
                
                # 获取样本数据
                get_sample_data(db, task_name, dataset_source)
                
                # 检查响应数量分布
                check_response_counts(db, task_name, dataset_source)
        else:
            print("\n❌ 没有找到commit相关的任务")
            print("💡 可能的原因:")
            print("  1. 数据还没有导入到数据库")
            print("  2. 任务名称或数据源名称不同")
            print("  3. 数据在不同的集合中")
        
    finally:
        client.close()

if __name__ == "__main__":
    main()
