#!/usr/bin/env python3
"""生成额外的 explorative_coding commit 响应，避免与 MongoDB 中已存在的重复。

用法:
  python generate_additional_commits.py --count 20            # 生成20个新的commit (默认)
  python generate_additional_commits.py --count 20 --dry-run  # 只显示将要处理的commit，不实际调用LLM

判定重复策略:
  以 commit 的 sha 为粒度，只选择数据库中尚无任何该 sha 相关记录的 commit。
  (数据库中 task_id 形如: task_pytorch_commits_<sha>_prompt_<N>_sampled)

假设:
  - 用户希望新增的是 20 个“新的 commit 项目”，而不是仅 20 条(prompt 尝试)记录。
  - 已部分生成的 commit 不再补齐其剩余 prompt/attempt，全部跳过；只挑完全全新的 commit。
如需改为“填补已有 commit 的剩余 attempts”，可后续扩展。
"""

import re
import csv
import argparse
from typing import Set, List, Dict, Any
from llm_response_generator import LLMResponseGenerator


TASK_NAME = "explorative_coding"
DATASET_SOURCE = "pytorch_commits"


def extract_processed_shas(generator: LLMResponseGenerator) -> Set[str]:
    """从 MongoDB 中提取已处理的 commit sha 集合。"""
    if generator.collection is None:
        raise RuntimeError("MongoDB collection 未初始化，检查 config.yaml output.format 和 mongo 设置")

    # 直接 distinct task_id 再解析 sha
    task_ids = generator.collection.distinct("task_id", {"dataset_source": DATASET_SOURCE})
    sha_set: Set[str] = set()
    pattern = re.compile(r"^task_pytorch_commits_(.+?)_prompt_\d+_sampled$")
    for tid in task_ids:
        m = pattern.match(tid)
        if m:
            sha_set.add(m.group(1))
    return sha_set


def load_commits_csv(csv_path: str) -> List[Dict[str, Any]]:
    commits: List[Dict[str, Any]] = []
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            commits.append(row)
    return commits


def pick_new_commits(all_commits: List[Dict[str, Any]], processed_shas: Set[str], count: int) -> List[Dict[str, Any]]:
    new_items: List[Dict[str, Any]] = []
    for row in all_commits:
        sha = row.get('sha')
        if not sha:
            continue
        if sha in processed_shas:
            continue
        new_items.append(row)
        if len(new_items) >= count:
            break
    return new_items


def main():
    parser = argparse.ArgumentParser(description="生成额外未处理的 commits explorative_coding 响应")
    parser.add_argument('--count', type=int, default=20, help='需要新增的 commit 数量 (默认20)')
    parser.add_argument('--dry-run', action='store_true', help='仅显示将要处理的 commit，不调用 LLM')
    parser.add_argument('--attempts-per-prompt', type=int, default=None, help='覆盖配置 attempts_per_prompt；缺省则用 config.yaml 中的值')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径 (默认 config.yaml)')
    args = parser.parse_args()

    generator = LLMResponseGenerator(config_file=args.config)
    task_cfg = generator.config.get('tasks', {}).get(TASK_NAME, {})
    data_file = task_cfg.get('data_file', 'sampled_commits.csv')

    print(f"[INFO] 读取已处理 commits (dataset_source={DATASET_SOURCE}) ...")
    processed_shas = extract_processed_shas(generator)
    print(f"[INFO] 已存在 {len(processed_shas)} 个不同 sha")

    print(f"[INFO] 加载 CSV: {data_file}")
    all_commits = load_commits_csv(data_file)
    print(f"[INFO] CSV 共 {len(all_commits)} 条 commit 记录")

    new_commits = pick_new_commits(all_commits, processed_shas, args.count)
    if not new_commits:
        print("[WARN] 没有找到可新增的未处理 commit。您可能已经处理了全部样本。")
        return

    print(f"[INFO] 计划新增 {len(new_commits)} 个未处理 commit (目标 {args.count})")
    for i, c in enumerate(new_commits, 1):
        print(f"  {i:02d}. {c.get('sha','')[:12]}  {c.get('date','')}  {c.get('message','')[:60].replace('\n',' ')}")

    if args.dry_run:
        print("[DRY-RUN] 结束。未进行任何 LLM 调用。")
        return

    # attempts_per_prompt 决策
    attempts = args.attempts_per_prompt or task_cfg.get('attempts_per_prompt', 6)
    print(f"[INFO] 使用 attempts_per_prompt = {attempts}")

    # 构造 processed_items 为 None (只处理全新 sha 不需跳过判断)
    # 直接调用底层 process_commits_data
    generator.process_commits_data(
        new_commits,
        attempts_per_prompt=attempts,
        run_id=None,               # 让函数内部生成新的 run_id
        processed_items={}         # 空集，全部视为新数据
    )

    print("[INFO] 额外 commit 生成流程完成。")


if __name__ == '__main__':
    main()
