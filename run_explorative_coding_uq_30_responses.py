#!/usr/bin/env python3
"""
专门处理有30个响应的 explorative_coding UQ 分析
只分析有30个响应的数据组，使用所有可用的UQ方法
"""

import os
import sys
import yaml
import logging
from typing import Dict, Any, List
from pymongo import MongoClient

# Add project root to path
sys.path.append(os.path.dirname(__file__))

# Create necessary directories
os.makedirs("logs", exist_ok=True)
os.makedirs("uq_analysis", exist_ok=True)

from uq_analysis.method_loader import UQMethodLoader
from uq_analysis.data_processor import DataProcessor
from uq_analysis.resume_manager import ResumeManager
from uq_analysis.progress_manager import ProgressManager, setup_logging

log = logging.getLogger(__name__)


class ExplorativeCodingUQ30ResponsesRunner:
    """专门处理有30个响应的explorative_coding UQ分析"""
    
    def __init__(self):
        """初始化分析器"""
        self.config = self.create_config()
        self.method_loader = UQMethodLoader()
        self.data_processor = DataProcessor(self.config["mongodb"])
        self.resume_manager = ResumeManager(self.config["resume"])
        self.progress_manager = ProgressManager(self.config["analysis"])
        
        # Setup logging
        setup_logging(self.config["logging"])
        
    def create_config(self) -> Dict[str, Any]:
        """创建专门的配置"""
        return {
            "mongodb": {
                "host": "localhost",
                "port": 27017,
                "database": "LLM-UQ",
                "source_collection": "response_collections",
                "timeout_ms": 30000
            },
            "resume": {
                "enabled": True,
                "save_interval": 10
            },
            "analysis": {
                "test_mode": False,
                "test_limit": None,
                "llm_model": "qwen3-32b",
                "progress_report_interval": 10
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file": "logs/explorative_coding_uq_30_responses.log"
            },
            "uq_methods": {
                "enabled_methods": [
                    "EigValLaplacianJaccardUQ",
                    "EigValLaplacianNLIUQ",
                    "EccentricityJaccardUQ",
                    "EccentricityNLIEntailUQ",
                    "SemanticEntropyNLIUQ",
                    "EmbeddingQwenUQ",
                    "EmbeddingE5UQ",
                    "LUQUQ",
                    "LofreeCPUQ",
                    "NumSetsUQ",
                    "KernelLanguageEntropyUQ"
                ],
                "method_params": {
                    "SemanticEntropyNLIUQ": {
                        "entailment_threshold": 0.5,
                        "strict_entailment": True
                    },
                    "EccentricityJaccardUQ": {
                        "thres": 0.9
                    },
                    "EccentricityNLIEntailUQ": {
                        "thres": 0.9,
                        "affinity": "entail"
                    },
                    "EigValLaplacianNLIUQ": {
                        "affinity": "entail"
                    },
                    "LofreeCPUQ": {
                        "lambda1": 1.0,
                        "lambda2": 1.0
                    }
                }
            }
        }
    
    def get_30_response_groups(self):
        """获取有30个响应的数据组"""
        collection = self.data_processor.db["response_collections"]
        
        pipeline = [
            {
                "$match": {
                    "task_name": "explorative_coding",
                    "dataset_source": "pytorch_commits"
                }
            },
            {
                "$group": {
                    "_id": {
                        "task_name": "$task_name",
                        "dataset_source": "$dataset_source",
                        "prompt_seed": "$prompt_seed",
                        "input_text": "$input_text",
                        "llm_model": "$llm_model"
                    },
                    "count": {"$sum": 1},
                    "docs": {"$push": "$$ROOT"}
                }
            },
            {
                "$match": {
                    "count": {"$eq": 30}  # 只处理有30个响应的组
                }
            }
        ]
        
        groups = list(collection.aggregate(pipeline))
        log.info(f"找到 {len(groups)} 个有30个响应的数据组")
        return groups
    
    def run_analysis(self):
        """运行UQ分析"""
        try:
            log.info("开始处理有30个响应的explorative_coding UQ分析...")
            
            # 连接MongoDB
            self.data_processor.connect_mongodb()
            
            # 先发现所有可用的方法
            discovered_methods = self.method_loader.discover_methods()
            log.info(f"发现 {len(discovered_methods)} 个UQ方法: {discovered_methods}")
            
            # 加载UQ方法
            enabled_methods = self.config["uq_methods"]["enabled_methods"]
            method_params = self.config["uq_methods"]["method_params"]
            uq_methods = self.method_loader.load_enabled_methods(enabled_methods, method_params)
            
            if not uq_methods:
                log.error("没有加载到UQ方法")
                return
            
            log.info(f"加载了 {len(uq_methods)} 个UQ方法: {list(uq_methods.keys())}")
            
            # 获取数据组
            groups = self.get_30_response_groups()
            if not groups:
                log.warning("没有找到有30个响应的数据组")
                return
            
            # 获取输出集合
            output_collection = self.data_processor.db["UQ_result_explorative_coding"]
            
            # 创建索引
            try:
                output_collection.create_index([
                    ("group_key.task_name", 1),
                    ("group_key.dataset_source", 1),
                    ("group_key.prompt_seed", 1)
                ], name="explorative_coding_uq_index", background=True)
            except Exception:
                pass
            
            # 加载已完成的工作
            method_names = list(uq_methods.keys())
            self.resume_manager.load_completed_work(output_collection, "explorative_coding", method_names)
            
            # 过滤待处理的工作
            pending_work = self.resume_manager.filter_pending_work(
                groups, method_names, "UQ_result_explorative_coding")
            
            if not pending_work:
                log.info("所有工作都已完成")
                return
            
            # 开始进度跟踪
            total_groups = len(pending_work)
            total_methods = len(method_names)
            self.progress_manager.start_analysis(1)
            self.progress_manager.start_task("explorative_coding/pytorch_commits", 
                                           total_groups, total_methods)
            
            log.info(f"开始处理 {total_groups} 个待处理的数据组")
            
            # 处理每个组
            for group_idx, (group, pending_methods) in enumerate(pending_work):
                group_key = self.data_processor.build_group_key(group["_id"])
                
                # 只计算待处理的方法
                pending_uq_methods = {name: uq_methods[name] for name in pending_methods}
                
                if not pending_uq_methods:
                    # 所有方法都已完成
                    for method_name in method_names:
                        self.progress_manager.log_skip("explorative_coding", group_key, method_name)
                    successful = 0
                    failed = 0
                    skipped = len(method_names)
                else:
                    # 计算UQ - 使用 raw_answer 字段
                    try:
                        llm_model = group["_id"].get("llm_model", "qwen3-32b")
                        result = self.data_processor.compute_uq_for_group(
                            group, "raw_answer", pending_uq_methods, llm_model)
                        
                        if result:
                            # 保存结果
                            self.data_processor.save_or_update_result(result, output_collection)
                            
                            successful = result["metadata"]["successful_methods"]
                            failed = result["metadata"]["failed_methods"]
                            skipped = len(method_names) - len(pending_methods)
                            
                            # 记录结果
                            for method_name, method_result in result["uq_results"].items():
                                if method_result["status"] == "success":
                                    uq_value = method_result["uq_value"]
                                    self.progress_manager.log_success(
                                        "explorative_coding", group_key, method_name, uq_value)
                                else:
                                    error = method_result.get("error", "Unknown error")
                                    self.progress_manager.log_error(
                                        "explorative_coding", group_key, method_name, error)
                            
                            # 标记为已完成
                            for method_name in pending_methods:
                                self.resume_manager.mark_work_completed(
                                    "UQ_result_explorative_coding", group_key, method_name)
                        else:
                            # 没有结果
                            failed = len(pending_methods)
                            successful = 0
                            skipped = len(method_names) - len(pending_methods)
                            
                            for method_name in pending_methods:
                                self.progress_manager.log_error(
                                    "explorative_coding", group_key, method_name, "Not enough responses")
                    
                    except Exception as e:
                        # 处理错误
                        failed = len(pending_methods)
                        successful = 0
                        skipped = len(method_names) - len(pending_methods)
                        
                        for method_name in pending_methods:
                            self.progress_manager.log_error(
                                "explorative_coding", group_key, method_name, str(e))
                
                # 更新进度
                self.progress_manager.update_group_progress(
                    "explorative_coding/pytorch_commits", group_idx, successful, failed, skipped)
            
            # 完成任务
            self.progress_manager.finish_task("explorative_coding/pytorch_commits")
            self.progress_manager.finish_analysis()
            log.info("UQ分析完成")
            
        except Exception as e:
            log.error(f"分析失败: {str(e)}")
            raise
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        try:
            self.data_processor.disconnect_mongodb()
        except Exception as e:
            log.error(f"清理时出错: {str(e)}")


def main():
    """主函数"""
    print("🚀 开始处理有30个响应的explorative_coding UQ分析...")
    
    runner = ExplorativeCodingUQ30ResponsesRunner()
    runner.run_analysis()
    
    print("✅ 分析完成！")


if __name__ == "__main__":
    main()
