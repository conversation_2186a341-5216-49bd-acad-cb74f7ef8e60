#!/usr/bin/env python3
"""
测试单个UQ方法
"""

import sys
import os
from pymongo import MongoClient
import logging

sys.path.append(os.path.dirname(__file__))

from uq_analysis.method_loader import UQMethodLoader
from uq_analysis.data_processor import DataProcessor

# 设置日志
logging.basicConfig(level=logging.DEBUG)
log = logging.getLogger(__name__)

def main():
    """测试单个UQ方法"""
    print("🧪 测试单个UQ方法...")
    
    # 连接MongoDB
    client = MongoClient("localhost", 27017)
    db = client["LLM-UQ"]
    collection = db["response_collections"]
    
    # 获取一个有30个响应的数据组
    pipeline = [
        {
            "$match": {
                "task_name": "sentiment_analysis",
                "dataset_source": "twitter_sentiment"
            }
        },
        {
            "$group": {
                "_id": {
                    "task_name": "$task_name",
                    "dataset_source": "$dataset_source",
                    "prompt_seed": "$prompt_seed",
                    "input_text": "$input_text",
                    "llm_model": "$llm_model"
                },
                "count": {"$sum": 1},
                "docs": {"$push": "$$ROOT"}
            }
        },
        {
            "$match": {
                "count": {"$eq": 30}
            }
        },
        {
            "$limit": 1
        }
    ]
    
    groups = list(collection.aggregate(pipeline))
    
    if not groups:
        print("❌ 没有找到数据组")
        return
    
    group = groups[0]
    print(f"📊 使用数据组: {group['_id']['prompt_seed']}")
    print(f"   响应数量: {group['count']}")
    
    # 初始化方法加载器
    method_loader = UQMethodLoader()
    discovered_methods = method_loader.discover_methods()
    print(f"🔧 发现 {len(discovered_methods)} 个方法")
    
    # 加载一个简单的方法进行测试
    test_method = method_loader.load_method("SemanticEntropyNLIUQ")
    if not test_method:
        print("❌ 无法加载测试方法")
        return
    
    print(f"✅ 加载了方法: {test_method.__class__.__name__}")
    
    # 初始化数据处理器
    mongodb_config = {
        "host": "localhost",
        "port": 27017,
        "database": "LLM-UQ",
        "source_collection": "response_collections"
    }
    
    data_processor = DataProcessor(mongodb_config)
    data_processor.connect_mongodb()
    
    # 测试UQ计算
    try:
        print("🔄 开始计算UQ...")
        
        # 检查数据组结构
        print(f"   Group keys: {list(group.keys())}")
        print(f"   Has 'docs': {'docs' in group}")
        print(f"   Docs count: {len(group.get('docs', []))}")
        
        if 'docs' in group and group['docs']:
            first_doc = group['docs'][0]
            print(f"   First doc has 'parsed_answer': {'parsed_answer' in first_doc}")
            print(f"   First doc parsed_answer: {first_doc.get('parsed_answer')}")
        
        # 计算UQ
        uq_methods = {"SemanticEntropyNLIUQ": test_method}
        result = data_processor.compute_uq_for_group(
            group, "parsed_answer", uq_methods, "qwen3-32b")
        
        if result:
            print("✅ UQ计算成功!")
            print(f"   成功方法数: {result['metadata']['successful_methods']}")
            print(f"   失败方法数: {result['metadata']['failed_methods']}")
            
            for method_name, method_result in result["uq_results"].items():
                print(f"   {method_name}: {method_result['status']} - {method_result.get('uq_value', 'N/A')}")
        else:
            print("❌ UQ计算返回None")
            
    except Exception as e:
        print(f"❌ UQ计算失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        data_processor.disconnect_mongodb()
        client.close()

if __name__ == "__main__":
    main()
