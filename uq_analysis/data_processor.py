#!/usr/bin/env python3
"""
Data Processing and Analysis Core Logic
Handles reading data from MongoDB, computing UQ, and storing results.
"""

import os
import sys
import logging
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Tuple
from collections import Counter
from pymongo import MongoClient
from pymongo.collection import Collection
from tqdm import tqdm

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from uq_methods.base import BaseUQMethod

log = logging.getLogger(__name__)


class DataProcessor:
    """Handles data processing and UQ computation."""
    
    def __init__(self, mongodb_config: Dict[str, Any]):
        """
        Initialize the data processor.
        
        Args:
            mongodb_config: MongoDB connection configuration
        """
        self.mongodb_config = mongodb_config
        self.client = None
        self.db = None
        self.source_collection = None
        
    def connect_mongodb(self):
        """Connect to MongoDB."""
        try:
            host = self.mongodb_config.get("host", "localhost")
            port = self.mongodb_config.get("port", 27017)
            database = self.mongodb_config.get("database", "LLM-UQ")
            source_collection = self.mongodb_config.get("source_collection", "response_collections")
            
            self.client = MongoClient(f'mongodb://{host}:{port}/')
            self.db = self.client[database]
            self.source_collection = self.db[source_collection]
            
            log.info(f"Connected to MongoDB: {host}:{port}/{database}")
            
        except Exception as e:
            log.error(f"Failed to connect to MongoDB: {str(e)}")
            raise
    
    def disconnect_mongodb(self):
        """Disconnect from MongoDB."""
        if self.client:
            self.client.close()
            log.info("Disconnected from MongoDB")
    
    def get_task_groups(self, task_name: str, dataset_source: str,
                       limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get groups of responses for a specific task.
        Groups by prompt_seed - each seed should have multiple responses for the same input.

        Args:
            task_name: Name of the task
            dataset_source: Source dataset name
            limit: Maximum number of groups to return (for testing)

        Returns:
            List of group dictionaries
        """
        # 根据不同的数据源使用不同的分组策略
        if dataset_source == "counterfactual_data":
            # Counterfactual数据使用category和row_index进行分组
            group_fields = {
                "task_name": "$task_name",
                "dataset_source": "$dataset_source",
                "prompt_seed": "$prompt_seed",
                "category": "$category",
                "row_index": "$row_index"
            }
            sort_fields = {"_id.category": 1, "_id.row_index": 1, "_id.prompt_seed": 1}
        elif dataset_source == "topic_model_labeling":
            # Topic modeling数据使用paper_name, doi, table_number, topic_number, key_terms, original_label, llm_model进行分组
            group_fields = {
                "task_name": "$task_name",
                "dataset_source": "$dataset_source",
                "paper_name": "$paper_name",
                "doi": "$doi",
                "table_number": "$table_number",
                "topic_number": "$topic_number",
                "key_terms": "$key_terms",
                "original_label": "$original_label",
                "llm_model": "$llm_model"
            }
            sort_fields = {"_id.paper_name": 1, "_id.table_number": 1, "_id.topic_number": 1, "_id.llm_model": 1}
        else:
            # 其他数据源使用原有的分组方式
            group_fields = {
                "task_name": "$task_name",
                "dataset_source": "$dataset_source",
                "prompt_seed": "$prompt_seed",
                "input_text": "$input_text"
            }
            sort_fields = {"_id.prompt_seed": 1}

        pipeline = [
            {
                "$match": {
                    "task_name": task_name,
                    "dataset_source": dataset_source
                }
            },
            {
                "$group": {
                    "_id": group_fields,
                    "count": {"$sum": 1},
                    "docs": {"$push": "$$ROOT"}
                }
            },
            {
                "$match": {
                    "count": {"$gte": 2}  # At least 2 responses for UQ computation
                }
            },
            {
                "$sort": sort_fields
            }
        ]

        if limit:
            pipeline.append({"$limit": limit})

        # 根据任务选择不同的collection
        if task_name == "topic_labeling":
            collection = self.db["topic_modeling_responses"]
        else:
            collection = self.source_collection

        groups = list(collection.aggregate(pipeline))
        log.info(f"Found {len(groups)} groups for {task_name}/{dataset_source}")

        return groups
    
    def extract_responses(self, docs: List[Dict[str, Any]], response_field: str) -> List[str]:
        """
        Extract responses from documents.
        
        Args:
            docs: List of documents
            response_field: Field name to extract responses from
            
        Returns:
            List of response strings
        """
        responses = []
        
        for doc in docs:
            response = doc.get(response_field)
            if response:
                responses.append(str(response))
        
        return responses
    
    def infer_reference_text(self, docs: List[Dict[str, Any]]) -> Optional[str]:
        """
        Infer reference text from documents.
        
        Args:
            docs: List of documents
            
        Returns:
            Most common reference answer or None
        """
        try:
            refs = [d.get("reference_answer") for d in docs if d.get("reference_answer")]
            if refs:
                return Counter(refs).most_common(1)[0][0]
        except Exception:
            pass
        return None
    
    def build_group_key(self, group_id: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build group key from group ID.

        Args:
            group_id: Group identifier from aggregation

        Returns:
            Standardized group key
        """
        base_key = {
            "task_name": group_id.get("task_name"),
            "dataset_source": group_id.get("dataset_source"),
        }

        # Add task-specific fields
        if group_id.get("dataset_source") == "topic_model_labeling":
            # For topic_labeling, include all the grouping fields
            base_key.update({
                "paper_name": group_id.get("paper_name"),
                "doi": group_id.get("doi"),
                "table_number": group_id.get("table_number"),
                "topic_number": group_id.get("topic_number"),
                "key_terms": group_id.get("key_terms"),
                "original_label": group_id.get("original_label"),
                "llm_model": group_id.get("llm_model"),
            })
        else:
            # For other tasks, use the original fields
            base_key.update({
                "prompt_seed": group_id.get("prompt_seed"),
                "input_text": group_id.get("input_text"),
            })

        return base_key
    
    def compute_uq_for_group(self, group: Dict[str, Any], response_field: str,
                           uq_methods: Dict[str, BaseUQMethod], llm_model: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Compute UQ for a single group using all enabled methods.

        Args:
            group: Group data containing documents
            response_field: Field to extract responses from
            uq_methods: Dictionary of UQ method instances

        Returns:
            Single UQ result record containing all methods
        """
        docs = group["docs"]
        group_id = group["_id"]

        # Extract llm_model from group data if not provided
        if llm_model is None:
            llm_model = group_id.get("llm_model", "unknown")

        # Build group key with additional info from docs
        group_key = self.build_group_key(group_id)

        # Add missing fields from first doc for different tasks
        if docs:
            first_doc = docs[0]

            if group_id.get("task_name") == "topic_labeling":
                # For topic_labeling, add prompt_seed and input_text
                group_key["prompt_seed"] = first_doc.get("prompt_seed")
                group_key["input_text"] = first_doc.get("input_text")
            elif group_id.get("task_name") == "counterfactual_qa":
                # For counterfactual_qa, add input_text (original question)
                group_key["input_text"] = first_doc.get("question") or first_doc.get("input_text")

        # Extract responses
        responses = self.extract_responses(docs, response_field)
        if len(responses) < 2:
            log.warning(f"Not enough responses for group {group_key}: {len(responses)}")
            return None

        # Get reference text
        reference_text = self.infer_reference_text(docs)

        # Initialize result record
        record = {
            "group_key": group_key,
            "llm_model": llm_model,  # Use extracted LLM model
            "uq_results": {},
            "metadata": {
                "n_responses": len(responses),
                "reference_text": reference_text,
                "response_field": response_field,
                "method_count": len(uq_methods),
                "successful_methods": 0,
                "failed_methods": 0
            },
            "timestamps": {
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }
        }

        # Compute UQ for each method
        for method_name, method_instance in uq_methods.items():
            try:
                # Set reference text if method supports it
                if hasattr(method_instance, "set_reference_text") and reference_text:
                    method_instance.set_reference_text(reference_text)

                # Compute uncertainty
                uq_result = method_instance.compute_uncertainty(responses)

                # Extract uncertainty score - handle 0 values correctly
                uq_value = None
                if "uncertainty_score" in uq_result:
                    uq_value = uq_result["uncertainty_score"]
                elif "uncertainty" in uq_result:
                    uq_value = uq_result["uncertainty"]
                elif "score" in uq_result:
                    uq_value = uq_result["score"]

                # Store method result
                record["uq_results"][method_name] = {
                    "method_info": {
                        "method_class": method_instance.__class__.__name__,
                        "method_module": method_instance.__class__.__module__,
                    },
                    "uq_value": uq_value,
                    "full_result": uq_result,
                    "status": "success",
                    "computed_at": datetime.now(timezone.utc)
                }

                record["metadata"]["successful_methods"] += 1
                log.debug(f"Computed UQ for {method_name}: {uq_value}")

            except Exception as e:
                log.error(f"Failed to compute UQ for {method_name}: {str(e)}")

                # Store error result
                record["uq_results"][method_name] = {
                    "method_info": {
                        "method_class": method_instance.__class__.__name__,
                        "method_module": method_instance.__class__.__module__,
                    },
                    "uq_value": None,
                    "error": str(e),
                    "status": "error",
                    "computed_at": datetime.now(timezone.utc)
                }

                record["metadata"]["failed_methods"] += 1

        return record
    
    def check_existing_results(self, output_collection: Collection,
                             group_key: Dict[str, Any], method_names: Optional[List[str]] = None) -> Dict[str, bool]:
        """
        Check if results already exist for a group and methods.

        Args:
            output_collection: MongoDB collection to check
            group_key: Group identifier
            method_names: List of method names to check (if None, check all)

        Returns:
            Dictionary mapping method names to existence status
        """
        query = {"group_key": group_key}

        existing_doc = output_collection.find_one(query)
        if not existing_doc:
            return {}

        existing_methods = existing_doc.get("uq_results", {})

        if method_names is None:
            return {method: True for method in existing_methods.keys()}
        else:
            return {method: method in existing_methods for method in method_names}
    
    def save_or_update_result(self, result: Dict[str, Any], output_collection: Collection):
        """
        Save or update UQ result to MongoDB using upsert.

        Args:
            result: Result record
            output_collection: MongoDB collection to save to
        """
        if not result:
            return

        try:
            # Use upsert to either insert new or update existing
            query = {"group_key": result["group_key"]}

            # For update, we need to merge the uq_results
            existing_doc = output_collection.find_one(query)
            if existing_doc:
                # Merge uq_results
                existing_uq_results = existing_doc.get("uq_results", {})
                new_uq_results = result["uq_results"]
                merged_uq_results = {**existing_uq_results, **new_uq_results}

                # Update metadata
                result["uq_results"] = merged_uq_results
                result["metadata"]["method_count"] = len(merged_uq_results)
                result["metadata"]["successful_methods"] = sum(
                    1 for r in merged_uq_results.values() if r.get("status") == "success"
                )
                result["metadata"]["failed_methods"] = sum(
                    1 for r in merged_uq_results.values() if r.get("status") == "error"
                )
                result["timestamps"]["updated_at"] = datetime.now(timezone.utc)

                # Keep original created_at if it exists
                if "created_at" in existing_doc.get("timestamps", {}):
                    result["timestamps"]["created_at"] = existing_doc["timestamps"]["created_at"]

            output_collection.replace_one(query, result, upsert=True)
            log.debug(f"Saved/updated result for group to {output_collection.name}")

        except Exception as e:
            log.error(f"Failed to save result: {str(e)}")
            raise
