#!/usr/bin/env python3
"""
为sentiment_analysis和topic_labeling任务计算embedding reference距离。

该脚本：
1. 从MongoDB读取原始数据和responses
2. 使用reference text作为参考
3. 使用parsed_answer作为答案
4. 计算embedding距离并更新CSV数据文件
"""

import sys
import argparse
import hashlib
import pickle
from pathlib import Path
from typing import List, Optional, Dict, Any
import pandas as pd
from tqdm import tqdm

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from pymongo import MongoClient
from uq_methods.implementations.embedding_e5 import EmbeddingE5UQ
from uq_methods.implementations.embedding_qwen import EmbeddingQwenUQ

# 配置
DATA_DIR = Path('uq_result_analysis/data')
CACHE_DIR = DATA_DIR / 'embedding_cache'
MONGO_HOST = 'localhost'
MONGO_PORT = 27017
DB_NAME = 'LLM-UQ'

# 确保缓存目录存在
CACHE_DIR.mkdir(parents=True, exist_ok=True)

def connect_mongo():
    """连接MongoDB"""
    client = MongoClient(MONGO_HOST, MONGO_PORT)
    db = client[DB_NAME]
    return db

def get_cache_key(responses: List[str], reference_text: str, embedding_method: str) -> str:
    """生成缓存键"""
    # 将所有输入组合成一个字符串，然后生成hash
    combined = f"{embedding_method}|{reference_text}|{'|'.join(sorted(responses))}"
    return hashlib.md5(combined.encode()).hexdigest()

def load_from_cache(cache_key: str) -> Optional[float]:
    """从缓存加载结果"""
    cache_file = CACHE_DIR / f"{cache_key}.pkl"
    if cache_file.exists():
        try:
            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            print(f"Error loading cache {cache_key}: {e}")
    return None

def save_to_cache(cache_key: str, result: float):
    """保存结果到缓存"""
    cache_file = CACHE_DIR / f"{cache_key}.pkl"
    try:
        with open(cache_file, 'wb') as f:
            pickle.dump(result, f)
    except Exception as e:
        print(f"Error saving cache {cache_key}: {e}")

def get_responses_and_reference_by_model_from_mongo(db, uq_collection_name: str, document_id: str) -> Dict[str, tuple[List[str], str]]:
    """从MongoDB获取指定document的parsed_answer作为responses和reference，按model分组"""
    try:
        from bson import ObjectId

        # 首先从UQ collection获取group_key
        uq_doc = db[uq_collection_name].find_one({'_id': ObjectId(document_id)})
        if not uq_doc or 'group_key' not in uq_doc:
            return {}

        group_key = uq_doc['group_key']
        task_name = group_key['task_name']

        # 根据任务类型选择不同的response collection、reference字段和model字段
        if task_name == 'topic_labeling':
            response_collection_name = 'topic_modeling_responses'
            reference_field = 'original_label'  # topic_labeling的reference在original_label字段
            model_field = 'llm_model'  # topic_labeling的model标识在llm_model字段
        else:
            response_collection_name = 'response_collections'
            reference_field = 'reference_answer'  # 其他任务的reference在reference_answer字段
            model_field = 'model_identifier'  # 其他任务的model标识在model_identifier字段

        # 使用group_key在对应的response collection中查找匹配的responses
        if task_name == 'topic_labeling':
            # 对于topic_labeling，需要更完整的匹配条件
            query = {
                'task_name': group_key['task_name'],
                'dataset_source': group_key['dataset_source'],
                'paper_name': group_key['paper_name'],
                'doi': group_key['doi'],
                'table_number': group_key['table_number'],
                'topic_number': group_key['topic_number'],
                'original_label': group_key['original_label'],
                'llm_model': group_key['llm_model']
            }
        else:
            # 其他任务使用原来的查询条件
            query = {
                'task_name': group_key['task_name'],
                'dataset_source': group_key['dataset_source'],
                'prompt_seed': group_key['prompt_seed'],
                'input_text': group_key['input_text']
            }

        response_docs = list(db[response_collection_name].find(query))

        # 对于topic_labeling，需要按llm_model + input_text + original_label分组
        # 这意味着每个(model, original_label)组合都是一个独立的组
        if task_name == 'topic_labeling':
            # 按(model_id, original_label)分组
            model_label_data = {}

            for doc in response_docs:
                model_id = doc.get(model_field)
                original_label = doc.get(reference_field)
                parsed_answer = doc.get('parsed_answer')

                if not model_id or not original_label or not parsed_answer:
                    continue

                # 使用(model_id, original_label)作为键
                key = f"{model_id}_{original_label}"

                if key not in model_label_data:
                    model_label_data[key] = {
                        'responses': [],
                        'reference_text': str(original_label),
                        'model_id': model_id
                    }

                model_label_data[key]['responses'].append(str(parsed_answer))

            # 转换为返回格式，但保持原来的model_id作为键
            result = {}
            for key, data in model_label_data.items():
                # 对于topic_labeling，我们需要为每个(model, label)组合创建一个条目
                result[key] = (data['responses'], data['reference_text'])

            return result

        else:
            # 其他任务按model分组
            model_data = {}

            for doc in response_docs:
                model_id = doc.get(model_field)
                if not model_id:
                    continue

                parsed_answer = doc.get('parsed_answer')
                reference_text = doc.get(reference_field)

                if not parsed_answer:
                    continue

                if model_id not in model_data:
                    model_data[model_id] = {
                        'responses': [],
                        'reference_text': str(reference_text) if reference_text else ""
                    }

                model_data[model_id]['responses'].append(str(parsed_answer))

            # 转换为返回格式
            result = {}
            for model_id, data in model_data.items():
                result[model_id] = (data['responses'], data['reference_text'])

            return result

    except Exception as e:
        print(f"Error getting responses and reference for {document_id}: {e}")
        return {}



def compute_single_embedding_distance(response: str, reference_text: str, embedding_method: str) -> Optional[float]:
    """计算单个response与reference的embedding距离，支持缓存"""
    try:
        # 预处理：转为小写
        processed_response = response.lower()
        processed_reference = reference_text.lower()

        # 如果response和reference相同，距离为0
        if processed_response == processed_reference:
            return 0.0

        # 生成缓存键（单个response）
        cache_key = get_cache_key([processed_response], processed_reference, embedding_method)

        # 尝试从缓存加载
        cached_result = load_from_cache(cache_key)
        if cached_result is not None:
            return cached_result

        # 使用embedding方法计算距离
        # 由于方法需要至少2个responses，我们传入两个相同的response
        if embedding_method.lower() == 'e5':
            method = EmbeddingE5UQ(reference_text=processed_reference)
        else:
            method = EmbeddingQwenUQ(reference_text=processed_reference)

        # 传入两个相同的response来满足最小数量要求
        result = method.compute_uncertainty([processed_response, processed_response])
        distance = result.get('avg_distance_to_reference') if result else None

        # 保存到缓存
        if distance is not None:
            save_to_cache(cache_key, distance)

        return distance
    except Exception as e:
        print(f"Error computing single embedding distance: {e}")
        return None

def compute_embedding_reference_distance_optimized(responses: List[str], reference_text: str, embedding_method: str = 'e5',
                                                input_text: str = "", verbose: bool = True) -> Optional[float]:
    """计算responses与reference的平均embedding距离，通过分组优化相同答案的情况"""
    try:
        # 预处理：转为小写
        processed_responses = [response.lower() for response in responses]
        processed_reference = reference_text.lower()

        # 统计每个unique response的出现次数
        from collections import Counter
        response_counts = Counter(processed_responses)
        unique_responses = list(response_counts.keys())

        if verbose:
            print(f"  [OPTIMIZED] Found {len(unique_responses)} unique responses out of {len(processed_responses)} total")
            print(f"    Input text: {input_text[:100]}{'...' if len(input_text) > 100 else ''}")
            print(f"    Reference text: {processed_reference[:100]}{'...' if len(processed_reference) > 100 else ''}")
            print(f"    Response distribution: {dict(response_counts)}")

        # 为每个unique response计算距离
        weighted_distances = []
        total_cached = 0
        total_computed = 0

        for unique_response in unique_responses:
            count = response_counts[unique_response]

            # 检查缓存
            cache_key = get_cache_key([unique_response], processed_reference, embedding_method)
            cached_distance = load_from_cache(cache_key)

            if cached_distance is not None:
                distance = cached_distance
                total_cached += count
                if verbose:
                    distance_str = f"{distance:.4f}" if distance is not None else "None"
                    print(f"    [CACHED] '{unique_response}' (×{count}): {distance_str}")
            else:
                # 计算距离
                distance = compute_single_embedding_distance(unique_response, processed_reference, embedding_method)
                total_computed += count
                if verbose:
                    distance_str = f"{distance:.4f}" if distance is not None else "None"
                    print(f"    [COMPUTED] '{unique_response}' (×{count}): {distance_str}")

            if distance is not None:
                # 根据出现次数加权
                weighted_distances.extend([distance] * count)

        if verbose:
            print(f"    Cache efficiency: {total_cached}/{len(processed_responses)} cached, {total_computed}/{len(processed_responses)} computed")

        # 计算加权平均距离
        if weighted_distances:
            avg_distance = sum(weighted_distances) / len(weighted_distances)
            if verbose:
                print(f"    Final average distance: {avg_distance:.4f}")
            return avg_distance
        else:
            if verbose:
                print(f"    No valid distances computed")
            return None

    except Exception as e:
        print(f"Error computing optimized embedding distance: {e}")
        return None

def get_document_info_from_mongo(db, uq_collection_name: str, document_id: str) -> Dict[str, Any]:
    """从MongoDB获取指定document的完整信息"""
    try:
        from bson import ObjectId

        # 从UQ collection获取document信息
        uq_doc = db[uq_collection_name].find_one({'_id': ObjectId(document_id)})
        if not uq_doc:
            return {}

        # 返回包含input_text和reference_text的字典
        group_key = uq_doc.get('group_key', {})
        return {
            'input_text': group_key.get('input_text', ''),
            'reference_answer': uq_doc.get('reference_answer', ''),
            'task_name': group_key.get('task_name', ''),
            'dataset_source': group_key.get('dataset_source', '')
        }

    except Exception as e:
        print(f"Error getting document info for {document_id}: {e}")
        return {}

def process_task_data(task: str, limit: int = 0) -> pd.DataFrame:
    """处理指定任务的数据，计算embedding reference距离"""
    print(f"Processing task: {task}")

    # 读取现有CSV数据
    csv_path = DATA_DIR / f"UQ_result_{task}.csv"
    if not csv_path.exists():
        raise FileNotFoundError(f"CSV file not found: {csv_path}")

    df = pd.read_csv(csv_path)
    print(f"Loaded {len(df)} rows from CSV")

    # 连接MongoDB
    db = connect_mongo()
    collection_name = f"UQ_result_{task}"

    # 获取需要处理的embedding方法行
    embedding_df = df[df['uq_method'].str.contains('Embedding', case=False, na=False)].copy()
    if embedding_df.empty:
        print("No embedding methods found in data")
        return df

    print(f"Found {len(embedding_df)} embedding method rows")

    # 按document_id分组处理
    unique_docs = embedding_df['document_id'].unique()
    if limit > 0:
        unique_docs = unique_docs[:limit]

    print(f"Processing {len(unique_docs)} unique documents")

    # 存储计算结果
    reference_distances = {}

    for i, doc_id in enumerate(tqdm(unique_docs, desc="Computing reference distances")):
        print(f"\n[{i+1}/{len(unique_docs)}] Processing document: {doc_id}")

        # 获取document完整信息
        doc_info = get_document_info_from_mongo(db, collection_name, doc_id)
        if not doc_info:
            print(f"  No document info found for {doc_id}")
            continue

        input_text = doc_info.get('input_text', '')

        print(f"  Task: {doc_info.get('task_name', 'unknown')}")
        print(f"  Dataset: {doc_info.get('dataset_source', 'unknown')}")

        # 获取按model分组的parsed_answer作为responses和reference
        model_data = get_responses_and_reference_by_model_from_mongo(db, collection_name, doc_id)
        if not model_data:
            print(f"  No model data found for document {doc_id}")
            continue

        print(f"  Found {len(model_data)} models")

        # 为每个model计算embedding距离
        for model_key, (responses, reference_text) in model_data.items():
            if not responses or not reference_text:
                print(f"    Model {model_key}: No responses or reference text")
                continue

            print(f"    Model {model_key}: {len(responses)} responses, reference: {reference_text}")

            # 计算E5和Qwen的reference距离
            print(f"      Computing E5 embedding distance...")
            e5_distance = compute_embedding_reference_distance_optimized(responses, reference_text, 'e5', input_text, verbose=True)

            print(f"      Computing Qwen embedding distance...")
            qwen_distance = compute_embedding_reference_distance_optimized(responses, reference_text, 'qwen', input_text, verbose=True)

            # 存储结果，使用doc_id和model_key的组合作为键
            key = f"{doc_id}_{model_key}"

            # 对于topic_labeling，model_key可能是"model_original_label"格式
            # 提取实际的model_id
            if doc_info.get('task_name') == 'topic_labeling' and '_' in model_key:
                actual_model_id = model_key.split('_')[0]  # 取第一部分作为model_id
            else:
                actual_model_id = model_key

            reference_distances[key] = {
                'document_id': doc_id,
                'model_id': actual_model_id,
                'model_key': model_key,  # 保存完整的键用于匹配
                'reference_text': reference_text,
                'e5_distance': e5_distance,
                'qwen_distance': qwen_distance
            }

            e5_result = f"{e5_distance:.4f}" if e5_distance is not None else "None"
            qwen_result = f"{qwen_distance:.4f}" if qwen_distance is not None else "None"
            print(f"      Results - E5: {e5_result}, Qwen: {qwen_result}")

    # 更新DataFrame
    print("Updating DataFrame with reference distances...")

    # 添加embedding_model和model_id信息到DataFrame（如果还没有的话）
    if 'embedding_model' not in df.columns:
        print("Adding embedding_model column to DataFrame...")
        df['embedding_model'] = ''

        # 从uq_method中提取embedding模型信息
        mask_e5 = df['uq_method'] == 'EmbeddingE5UQ'
        mask_qwen = df['uq_method'] == 'EmbeddingQwenUQ'
        df.loc[mask_e5, 'embedding_model'] = 'E5'
        df.loc[mask_qwen, 'embedding_model'] = 'Qwen'

    if 'model_id' not in df.columns:
        print("Adding model_id column to DataFrame...")
        df['model_id'] = ''

        # 从llm_model列中提取model_id信息
        # 对于embedding方法，model_id应该与llm_model相同
        mask_embedding = df['uq_method'].str.contains('Embedding', case=False, na=False)
        df.loc[mask_embedding, 'model_id'] = df.loc[mask_embedding, 'llm_model']

    # 分别更新E5和Qwen的距离
    updated_count = 0

    for key, data in reference_distances.items():
        doc_id = data['document_id']
        model_id = data['model_id']
        e5_distance = data['e5_distance']
        qwen_distance = data['qwen_distance']

        # 更新E5距离
        mask_e5 = (df['document_id'] == doc_id) & (df['model_id'] == model_id) & (df['uq_method'] == 'EmbeddingE5UQ')
        if mask_e5.any():
            df.loc[mask_e5, 'avg_distance_to_reference'] = e5_distance
            updated_count += mask_e5.sum()

        # 更新Qwen距离
        mask_qwen = (df['document_id'] == doc_id) & (df['model_id'] == model_id) & (df['uq_method'] == 'EmbeddingQwenUQ')
        if mask_qwen.any():
            df.loc[mask_qwen, 'avg_distance_to_reference'] = qwen_distance
            updated_count += mask_qwen.sum()

    print(f"Updated {updated_count} embedding method rows")

    return df

def main():
    parser = argparse.ArgumentParser(description="计算embedding reference距离")
    parser.add_argument('--task', required=True, choices=['sentiment_analysis', 'topic_labeling'], 
                       help='任务类型')
    parser.add_argument('--limit', type=int, default=0, 
                       help='限制处理的document数量（0表示处理全部）')
    parser.add_argument('--output-suffix', default='_with_reference', 
                       help='输出文件后缀')
    
    args = parser.parse_args()
    
    try:
        # 处理数据
        updated_df = process_task_data(args.task, args.limit)
        
        # 保存更新后的数据
        output_path = DATA_DIR / f"UQ_result_{args.task}{args.output_suffix}.csv"
        updated_df.to_csv(output_path, index=False)
        print(f"Updated data saved to: {output_path}")
        
        # 显示统计信息
        embedding_rows = updated_df[updated_df['uq_method'].str.contains('Embedding', case=False, na=False)]
        non_null_ref = embedding_rows['avg_distance_to_reference'].notna().sum()
        total_embedding = len(embedding_rows)
        
        print(f"\nStatistics:")
        print(f"Total embedding method rows: {total_embedding}")
        print(f"Rows with reference distance: {non_null_ref}")
        print(f"Coverage: {non_null_ref/total_embedding*100:.1f}%")
        
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
